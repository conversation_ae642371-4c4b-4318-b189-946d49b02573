defmodule Drops.Relation.SchemaCache do
  @moduledoc """
  Persistent cache for inferred Drops.Relation schemas based on migration versions.

  This module provides efficient caching of schema inference results to avoid
  redundant database introspection during compilation. The cache persists across
  application restarts using DETS (Disk Erlang Term Storage) and is invalidated
  automatically when new migrations are applied, ensuring schemas stay in sync
  with the database structure.

  ## Features

  - DETS-based persistent caching that survives application restarts
  - Migration version-based cache invalidation
  - Multi-repository support
  - Automatic cache cleanup and size management
  - Fallback to in-memory storage if disk access fails

  ## Usage

      # Get cached schema or infer if not cached
      schema = Drops.Relation.SchemaCache.get_or_infer_schema(
        MyApp.Repo,
        "users",
        fn -> expensive_inference_operation() end
      )

      # Clear cache for a specific repository
      Drops.Relation.SchemaCache.clear_repo_cache(MyApp.Repo)

      # Get cache statistics
      stats = Drops.Relation.SchemaCache.stats()
  """

  use GenServer
  require Logger

  alias Drops.Config

  @dets_table :drops_schema_cache
  @cache_file_name "drops_schema_cache.dets"

  ## Public API

  @doc """
  Starts the schema cache GenServer.
  """
  @spec start_link(keyword()) :: GenServer.on_start()
  def start_link(opts \\ []) do
    GenServer.start_link(__MODULE__, opts, name: __MODULE__)
  end

  @doc """
  Gets a cached schema or infers it using the provided function.

  If a valid cached schema exists for the given repository and table name,
  it returns the cached version. Otherwise, it calls the inference function,
  caches the result, and returns it.

  ## Parameters

  - `repo` - The Ecto repository module
  - `table_name` - The database table name
  - `infer_fn` - Function that performs schema inference when cache miss occurs

  ## Returns

  The inferred schema (either from cache or freshly computed).

  ## Examples

      schema = get_or_infer_schema(MyApp.Repo, "users", fn ->
        # Expensive inference logic here
        Drops.Relation.infer_schema_direct(relation, name, repo)
      end)
  """
  @spec get_or_infer_schema(module(), String.t(), (-> any())) :: any()
  def get_or_infer_schema(repo, table_name, infer_fn) when is_function(infer_fn, 0) do
    if cache_enabled?() do
      case get_cached_schema(repo, table_name) do
        {:hit, schema} ->
          log_cache_event("Schema cache hit for #{repo}.#{table_name}", :debug)
          schema

        :miss ->
          log_cache_event(
            "Schema cache miss for #{repo}.#{table_name}, inferring...",
            :debug
          )

          schema = infer_fn.()
          cache_schema(repo, table_name, schema)
          schema
      end
    else
      infer_fn.()
    end
  end

  @doc """
  Clears all cached schemas for a specific repository.

  This is useful when you know the database structure has changed
  and want to force re-inference for all tables in a repository.

  ## Parameters

  - `repo` - The Ecto repository module to clear cache for

  ## Examples

      Drops.Relation.SchemaCache.clear_repo_cache(MyApp.Repo)
  """
  @spec clear_repo_cache(module()) :: :ok
  def clear_repo_cache(repo) do
    GenServer.call(__MODULE__, {:clear_repo_cache, repo})
  end

  @doc """
  Clears the entire schema cache.

  This removes all cached schemas for all repositories.
  """
  @spec clear_all() :: :ok
  def clear_all do
    GenServer.call(__MODULE__, :clear_all)
  end

  @doc """
  Returns cache statistics including hit/miss ratios and entry counts.

  ## Returns

  A map containing:
  - `:total_entries` - Total number of cached schemas
  - `:hits` - Number of cache hits since start
  - `:misses` - Number of cache misses since start
  - `:hit_ratio` - Cache hit ratio as a percentage

  ## Examples

      %{
        total_entries: 15,
        hits: 142,
        misses: 8,
        hit_ratio: 94.67
      } = Drops.Relation.SchemaCache.stats()
  """
  @spec stats() :: map()
  def stats do
    GenServer.call(__MODULE__, :stats)
  end

  ## Private API

  defp get_cached_schema(repo, table_name) do
    current_version = get_migration_version(repo)
    cache_key = {repo, table_name}

    case :dets.lookup(@dets_table, cache_key) do
      [{^cache_key, schema, ^current_version}] ->
        GenServer.cast(__MODULE__, :cache_hit)
        {:hit, schema}

      [{^cache_key, _schema, _old_version}] ->
        # Version mismatch, remove stale entry
        :dets.delete(@dets_table, cache_key)
        GenServer.cast(__MODULE__, :cache_miss)
        :miss

      [] ->
        GenServer.cast(__MODULE__, :cache_miss)
        :miss
    end
  end

  defp cache_schema(repo, table_name, schema) do
    if cache_enabled?() do
      version = get_migration_version(repo)
      cache_key = {repo, table_name}
      :dets.insert(@dets_table, {cache_key, schema, version})
      # Ensure data is written to disk
      :dets.sync(@dets_table)

      # Check if we need to enforce max entries limit
      GenServer.cast(__MODULE__, :check_size_limit)
    end
  end

  defp get_migration_version(repo) do
    try do
      # Query the schema_migrations table for the latest version
      query = "SELECT version FROM schema_migrations ORDER BY version DESC LIMIT 1"

      case repo.query(query, [], skip_org_id: true) do
        {:ok, %{rows: [[version]]}} when is_binary(version) ->
          version

        {:ok, %{rows: [[version]]}} when is_integer(version) ->
          Integer.to_string(version)

        {:ok, %{rows: []}} ->
          # No migrations have been run yet
          "0"

        {:error, _reason} ->
          # schema_migrations table doesn't exist or other error
          # This can happen in test environments or fresh databases
          "0"
      end
    rescue
      # Handle cases where repo is not available or configured
      _ -> "0"
    end
  end

  defp cache_enabled? do
    try do
      Config.schema_cache()[:enabled]
    rescue
      RuntimeError ->
        # Fallback to application environment during compilation
        # when the Drops application hasn't started yet
        Application.get_env(:drops, :schema_cache, enabled: true)[:enabled]
    end
  end

  defp max_entries do
    Config.schema_cache()[:max_entries]
  end

  defp cleanup_interval do
    Config.schema_cache()[:cleanup_interval]
  end

  defp cache_absolute_directory do
    Path.join(File.cwd!(), cache_relative_dir())
  end

  defp cache_relative_dir do
    Path.join(["tmp", "cache", Mix.env() |> Atom.to_string(), "drops_schema"])
  end

  ## GenServer Callbacks

  @impl GenServer
  def init(_opts) do
    cache_dir = cache_absolute_directory()
    File.mkdir_p!(cache_dir)

    cache_file = Path.join(cache_dir, @cache_file_name)

    case :dets.open_file(@dets_table, [
           {:file, String.to_charlist(cache_file)},
           {:type, :set}
         ]) do
      {:ok, @dets_table} ->
        log_cache_event(
          "Drops.Relation.SchemaCache started with persistent storage at #{cache_relative_dir()}/#{@cache_file_name}",
          :info
        )

      {:error, reason} ->
        log_cache_event(
          "Failed to open DETS cache file: #{inspect(reason)}, falling back to in-memory",
          :warning
        )

        # Fallback to ETS if DETS fails
        :ets.new(@dets_table, [:named_table, :public, :set, {:read_concurrency, true}])
    end

    # Schedule periodic cleanup
    if cleanup_interval() != :never do
      Process.send_after(self(), :cleanup, cleanup_interval())
    end

    {:ok, %{hits: 0, misses: 0}}
  end

  @impl GenServer
  def terminate(_reason, _state) do
    :dets.close(@dets_table)
    :ok
  end

  @impl GenServer
  def handle_call({:clear_repo_cache, repo}, _from, state) do
    # Find and delete all entries for this repo
    match_pattern = {{repo, :_}, :_, :_}
    :dets.match_delete(@dets_table, match_pattern)
    :dets.sync(@dets_table)

    Logger.info("Cleared schema cache for repository: #{inspect(repo)}")
    {:reply, :ok, state}
  end

  @impl GenServer
  def handle_call(:clear_all, _from, state) do
    :dets.delete_all_objects(@dets_table)
    :dets.sync(@dets_table)
    log_cache_event("Cleared entire schema cache", :info)
    {:reply, :ok, %{state | hits: 0, misses: 0}}
  end

  @impl GenServer
  def handle_call(:stats, _from, %{hits: hits, misses: misses} = state) do
    total_requests = hits + misses
    hit_ratio = if total_requests > 0, do: hits / total_requests * 100, else: 0.0

    stats = %{
      total_entries: :dets.info(@dets_table, :size),
      hits: hits,
      misses: misses,
      hit_ratio: Float.round(hit_ratio, 2)
    }

    {:reply, stats, state}
  end

  @impl GenServer
  def handle_cast(:cache_hit, %{hits: hits} = state) do
    {:noreply, %{state | hits: hits + 1}}
  end

  @impl GenServer
  def handle_cast(:cache_miss, %{misses: misses} = state) do
    {:noreply, %{state | misses: misses + 1}}
  end

  @impl GenServer
  def handle_cast(:check_size_limit, state) do
    current_size = :dets.info(@dets_table, :size)

    if current_size > max_entries() do
      # Simple LRU-like cleanup: remove oldest entries
      # This is a basic implementation - could be improved with proper LRU
      entries_to_remove = current_size - max_entries() + div(max_entries(), 10)
      remove_oldest_entries(entries_to_remove)

      Logger.debug(
        "Schema cache size limit reached, removed #{entries_to_remove} entries"
      )
    end

    {:noreply, state}
  end

  @impl GenServer
  def handle_info(:cleanup, state) do
    # Periodic cleanup of potentially stale entries
    cleanup_stale_entries()

    # Schedule next cleanup
    Process.send_after(self(), :cleanup, cleanup_interval())

    {:noreply, state}
  end

  ## Private Helpers

  defp remove_oldest_entries(count) do
    # Simple approach: remove random entries
    # In a production system, you might want to implement proper LRU
    all_keys = :dets.match(@dets_table, {:"$1", :_, :_})

    all_keys
    |> Enum.shuffle()
    |> Enum.take(count)
    |> Enum.each(fn [key] -> :dets.delete(@dets_table, key) end)

    :dets.sync(@dets_table)
  end

  defp cleanup_stale_entries do
    # Remove entries where the migration version no longer matches
    # This handles cases where migrations were rolled back
    all_entries = :dets.match_object(@dets_table, :_)

    stale_entries =
      Enum.filter(all_entries, fn {{repo, _table}, _schema, cached_version} ->
        current_version = get_migration_version(repo)
        cached_version != current_version
      end)

    Enum.each(stale_entries, fn {{repo, table}, _schema, _version} ->
      :dets.delete(@dets_table, {repo, table})
    end)

    if length(stale_entries) > 0 do
      :dets.sync(@dets_table)
      log_cache_event("Cleaned up #{length(stale_entries)} stale cache entries", :debug)
    end
  end

  # Log cache events to appropriate logger based on environment
  defp log_cache_event(message, level) do
    case Mix.env() do
      :test ->
        # In test environment, log to drops_test logger (file)
        Logger.log(level, message, logger: :drops_test)

      _ ->
        # In other environments, use default logger
        Logger.log(level, message)
    end
  end
end
